using UnityEngine;
using Mirror;
using System.Collections;
using System.Collections.Generic;
using System;
using System.Linq;

/// <summary>
/// Comprehensive scene transition manager that coordinates all aspects of scene transitions
/// from MainMenu to Map_01, ensuring proper state management, error handling, and Steam integration.
/// </summary>
public class SceneTransitionManager : NetworkBehaviour
{
    public static SceneTransitionManager Instance { get; private set; }

    [Header("Transition Configuration")]
    [SerializeField] private float transitionTimeout = 30f;
    [SerializeField] private float playerSpawnTimeout = 15f;
    [SerializeField] private float steamValidationTimeout = 10f;
    [SerializeField] private bool enableDetailedLogging = true;

    [Header("Scene References")]
    [SerializeField] private string mainMenuSceneName = "MainMenu";
    [SerializeField] private string map01SceneName = "Map_01";

    // Transition state tracking
    public enum TransitionState
    {
        Idle,
        PreparingTransition,
        ValidatingPlayers,
        CleaningMainMenu,
        LoadingScene,
        SpawningPlayers,
        InitializingGameplay,
        Complete,
        Failed
    }

    [SyncVar(hook = nameof(OnTransitionStateChanged))]
    private TransitionState currentTransitionState = TransitionState.Idle;

    [SyncVar]
    private float transitionProgress = 0f;

    // Player state tracking
    private Dictionary<int, PlayerTransitionData> playerTransitionStates = new Dictionary<int, PlayerTransitionData>();
    private HashSet<int> playersReadyForTransition = new HashSet<int>();
    private HashSet<int> playersSpawned = new HashSet<int>();

    // Transition data
    private TransitionData currentTransition;
    private Coroutine transitionCoroutine;

    // Events
    public static event Action<TransitionState> OnTransitionStateChangedEvent;
    public static event Action<float> OnTransitionProgressUpdated;
    public static event Action<string> OnTransitionError;

    public TransitionState CurrentState => currentTransitionState;
    public float TransitionProgress => transitionProgress;

    private struct PlayerTransitionData
    {
        public int connectionId;
        public bool isReady;
        public bool hasValidSteamId;
        public bool isSpawned;
        public bool isConfigured;
        public float lastUpdateTime;
    }

    private struct TransitionData
    {
        public string targetScene;
        public List<int> participatingPlayers;
        public float startTime;
        public bool isMultiplayer;
        public Dictionary<string, object> transitionParameters;
    }

    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else if (Instance != this)
        {
            Debug.LogWarning("SceneTransitionManager: Multiple instances detected. Destroying duplicate.");
            Destroy(gameObject);
            return;
        }
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        InitializeTransitionManager();
    }

    public override void OnStartClient()
    {
        base.OnStartClient();
        if (!isServer)
        {
            InitializeTransitionManager();
        }
    }

    private void InitializeTransitionManager()
    {
        currentTransitionState = TransitionState.Idle;
        transitionProgress = 0f;
        playerTransitionStates.Clear();
        playersReadyForTransition.Clear();
        playersSpawned.Clear();

        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: Initialized successfully");
        }
    }

    /// <summary>
    /// Initiates a scene transition from MainMenu to Map_01
    /// </summary>
    [Server]
    public bool StartTransitionToMap01()
    {
        if (!isServer)
        {
            Debug.LogError("SceneTransitionManager: StartTransitionToMap01 can only be called on server");
            return false;
        }

        if (currentTransitionState != TransitionState.Idle)
        {
            Debug.LogWarning($"SceneTransitionManager: Cannot start transition, current state: {currentTransitionState}");
            return false;
        }

        // Validate prerequisites
        if (!ValidateTransitionPrerequisites())
        {
            return false;
        }

        // Initialize transition data
        currentTransition = new TransitionData
        {
            targetScene = map01SceneName,
            participatingPlayers = GetParticipatingPlayerIds(),
            startTime = Time.time,
            isMultiplayer = MyNetworkManager.isMultiplayer,
            transitionParameters = new Dictionary<string, object>()
        };

        // Start transition process
        if (transitionCoroutine != null)
        {
            StopCoroutine(transitionCoroutine);
        }

        transitionCoroutine = StartCoroutine(ExecuteSceneTransition());
        return true;
    }

    private bool ValidateTransitionPrerequisites()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: Performing comprehensive transition validation");
        }

        // Perform comprehensive validation using the validator
        var validationReports = SceneTransitionValidator.ValidateTransitionPrerequisites();

        // Log validation results
        SceneTransitionValidator.LogValidationReports(validationReports, "Pre-Transition");

        // Check for blocking issues
        if (SceneTransitionValidator.HasBlockingIssues(validationReports))
        {
            LogTransitionError($"Transition validation failed: {SceneTransitionValidator.GetValidationSummary(validationReports)}");
            return false;
        }

        // Log any warnings but allow transition to continue
        var warnings = validationReports.Where(r => r.result == SceneTransitionValidator.ValidationResult.Warning).ToList();
        if (warnings.Count > 0)
        {
            Debug.LogWarning($"SceneTransitionManager: Proceeding with {warnings.Count} warnings");
        }

        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: Transition validation passed");
        }

        return true;
    }



    private List<int> GetParticipatingPlayerIds()
    {
        List<int> playerIds = new List<int>();

        if (LobbyPlayerList.instance != null)
        {
            foreach (var client in LobbyPlayerList.instance.allClients)
            {
                if (client != null && client.connectionToClient != null)
                {
                    playerIds.Add(client.connectionToClient.connectionId);
                }
            }
        }

        return playerIds;
    }

    private IEnumerator ExecuteSceneTransition()
    {
        float startTime = Time.time;
        bool transitionSuccessful = true;

        // Phase 1: Prepare for transition
        yield return StartCoroutine(PrepareForTransition());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Phase 2: Validate all players
        yield return StartCoroutine(ValidateAllPlayers());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Phase 3: Clean up MainMenu scene
        yield return StartCoroutine(CleanupMainMenuScene());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Phase 4: Load target scene
        yield return StartCoroutine(LoadTargetScene());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Phase 5: Spawn and configure players
        yield return StartCoroutine(SpawnAndConfigurePlayers());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Phase 6: Initialize gameplay systems
        yield return StartCoroutine(InitializeGameplaySystems());
        if (currentTransitionState == TransitionState.Failed)
        {
            transitionSuccessful = false;
            yield break;
        }

        // Handle transition completion
        if (transitionSuccessful)
        {
            SetTransitionState(TransitionState.Complete);
            UpdateTransitionProgress(1f);

            if (enableDetailedLogging)
            {
                float totalTime = Time.time - startTime;
                Debug.Log($"SceneTransitionManager: Transition completed successfully in {totalTime:F2} seconds");
            }
        }
        else
        {
            LogTransitionError("Transition failed during execution");
            SetTransitionState(TransitionState.Failed);
        }
    }

    private IEnumerator PrepareForTransition()
    {
        SetTransitionState(TransitionState.PreparingTransition);
        UpdateTransitionProgress(0.1f);

        // Initialize player transition states
        foreach (int playerId in currentTransition.participatingPlayers)
        {
            playerTransitionStates[playerId] = new PlayerTransitionData
            {
                connectionId = playerId,
                isReady = false,
                hasValidSteamId = false,
                isSpawned = false,
                isConfigured = false,
                lastUpdateTime = Time.time
            };
        }

        // Notify clients about transition start
        RpcNotifyTransitionStart(currentTransition.targetScene);

        yield return new WaitForSeconds(0.5f); // Allow time for client preparation
    }

    private IEnumerator ValidateAllPlayers()
    {
        SetTransitionState(TransitionState.ValidatingPlayers);
        UpdateTransitionProgress(0.2f);

        float validationStartTime = Time.time;

        // Validate each player's state
        foreach (int playerId in currentTransition.participatingPlayers)
        {
            if (!ValidatePlayerForTransition(playerId))
            {
                LogTransitionError($"Player {playerId} failed validation");
                SetTransitionState(TransitionState.Failed);
                yield break;
            }
        }

        // Wait for all players to confirm readiness
        while (playersReadyForTransition.Count < currentTransition.participatingPlayers.Count)
        {
            if (Time.time - validationStartTime > steamValidationTimeout)
            {
                LogTransitionError("Player validation timeout");
                SetTransitionState(TransitionState.Failed);
                yield break;
            }

            yield return new WaitForSeconds(0.1f);
        }

        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: All players validated successfully");
        }
    }

    private bool ValidatePlayerForTransition(int playerId)
    {
        // Find the corresponding client
        MyClient client = FindClientByConnectionId(playerId);
        if (client == null)
        {
            return false;
        }

        // Validate Steam ID if multiplayer
        if (currentTransition.isMultiplayer)
        {
            if (client.playerInfo.steamId == 0)
            {
                return false;
            }
        }

        // Mark player as ready for transition
        playersReadyForTransition.Add(playerId);
        return true;
    }

    private MyClient FindClientByConnectionId(int connectionId)
    {
        if (LobbyPlayerList.instance != null)
        {
            foreach (var client in LobbyPlayerList.instance.allClients)
            {
                if (client != null && client.connectionToClient != null && 
                    client.connectionToClient.connectionId == connectionId)
                {
                    return client;
                }
            }
        }
        return null;
    }

    private IEnumerator CleanupMainMenuScene()
    {
        SetTransitionState(TransitionState.CleaningMainMenu);
        UpdateTransitionProgress(0.3f);

        // Reset player ready states
        if (LobbyPlayerList.instance != null)
        {
            foreach (var client in LobbyPlayerList.instance.allClients)
            {
                if (client != null)
                {
                    client.IsReady = false;
                }
            }
        }

        // Destroy MainMenu instance
        if (MainMenu.instance != null)
        {
            Destroy(MainMenu.instance.gameObject);
        }

        yield return new WaitForEndOfFrame(); // Allow cleanup to complete
    }

    private IEnumerator LoadTargetScene()
    {
        SetTransitionState(TransitionState.LoadingScene);
        UpdateTransitionProgress(0.5f);

        bool sceneLoadSuccessful = false;

        // Initiate scene change
        if (InitiateSceneChangeSync())
        {
            // Wait for scene to load
            float loadStartTime = Time.time;
            while (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name != currentTransition.targetScene)
            {
                if (Time.time - loadStartTime > transitionTimeout)
                {
                    LogTransitionError("Scene loading timeout");
                    SetTransitionState(TransitionState.Failed);
                    yield break;
                }

                yield return new WaitForSeconds(0.1f);
            }

            sceneLoadSuccessful = true;
        }

        if (sceneLoadSuccessful)
        {
            // Validate the loaded scene
            bool sceneValidationPassed = ValidateLoadedSceneSync();

            if (!sceneValidationPassed)
            {
                SetTransitionState(TransitionState.Failed);
                yield break;
            }

            if (enableDetailedLogging)
            {
                Debug.Log($"SceneTransitionManager: Scene '{currentTransition.targetScene}' loaded and validated successfully");
            }
        }
        else
        {
            SetTransitionState(TransitionState.Failed);
        }
    }

    private bool InitiateSceneChangeSync()
    {
        try
        {
            NetworkManager.singleton.ServerChangeScene(currentTransition.targetScene);
            return true;
        }
        catch (System.Exception ex)
        {
            LogTransitionError($"Exception during scene change initiation: {ex.Message}");
            return false;
        }
    }

    private bool ValidateLoadedSceneSync()
    {
        try
        {
            var sceneValidationReports = SceneTransitionValidator.ValidateMap01SceneState();
            SceneTransitionValidator.LogValidationReports(sceneValidationReports, "Post-Scene-Load");

            if (SceneTransitionValidator.HasBlockingIssues(sceneValidationReports))
            {
                LogTransitionError($"Scene validation failed after loading: {SceneTransitionValidator.GetValidationSummary(sceneValidationReports)}");
                return false;
            }

            return true;
        }
        catch (System.Exception ex)
        {
            LogTransitionError($"Exception during scene validation: {ex.Message}");
            return false;
        }
    }

    private IEnumerator SpawnAndConfigurePlayers()
    {
        SetTransitionState(TransitionState.SpawningPlayers);
        UpdateTransitionProgress(0.7f);

        // Players will be spawned automatically by MyNetworkManager.OnServerAddPlayer
        // We just need to wait for all players to be spawned and configured

        float spawnStartTime = Time.time;
        while (playersSpawned.Count < currentTransition.participatingPlayers.Count)
        {
            if (Time.time - spawnStartTime > playerSpawnTimeout)
            {
                LogTransitionError("Player spawning timeout");
                SetTransitionState(TransitionState.Failed);
                yield break;
            }

            yield return new WaitForSeconds(0.1f);
        }

        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: All players spawned and configured successfully");
        }
    }

    private IEnumerator InitializeGameplaySystems()
    {
        SetTransitionState(TransitionState.InitializingGameplay);
        UpdateTransitionProgress(0.9f);

        // Wait for ForestGameManager to initialize
        ForestGameManager forestGameManager = FindObjectOfType<ForestGameManager>();
        if (forestGameManager == null)
        {
            LogTransitionError("ForestGameManager not found in target scene");
            SetTransitionState(TransitionState.Failed);
            yield break;
        }

        // Wait a moment for all systems to initialize
        yield return new WaitForSeconds(1f);

        if (enableDetailedLogging)
        {
            Debug.Log("SceneTransitionManager: Gameplay systems initialized successfully");
        }
    }

    /// <summary>
    /// Called by MyNetworkManager when a player is successfully spawned
    /// </summary>
    public void NotifyPlayerSpawned(int connectionId)
    {
        if (currentTransitionState == TransitionState.SpawningPlayers)
        {
            playersSpawned.Add(connectionId);

            if (enableDetailedLogging)
            {
                Debug.Log($"SceneTransitionManager: Player {connectionId} spawned ({playersSpawned.Count}/{currentTransition.participatingPlayers.Count})");
            }
        }
    }

    [ClientRpc]
    private void RpcNotifyTransitionStart(string targetScene)
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"SceneTransitionManager: Client notified of transition to {targetScene}");
        }

        // Clients can perform any necessary preparation here
    }

    private void SetTransitionState(TransitionState newState)
    {
        if (currentTransitionState != newState)
        {
            TransitionState oldState = currentTransitionState;
            currentTransitionState = newState;

            if (enableDetailedLogging)
            {
                Debug.Log($"SceneTransitionManager: State changed from {oldState} to {newState}");
            }
        }
    }

    private void UpdateTransitionProgress(float progress)
    {
        transitionProgress = Mathf.Clamp01(progress);
        OnTransitionProgressUpdated?.Invoke(transitionProgress);
    }

    private void OnTransitionStateChanged(TransitionState oldState, TransitionState newState)
    {
        OnTransitionStateChangedEvent?.Invoke(newState);
    }

    private void LogTransitionError(string error)
    {
        Debug.LogError($"SceneTransitionManager: {error}");
        OnTransitionError?.Invoke(error);
    }

    /// <summary>
    /// Force abort current transition (emergency use only)
    /// </summary>
    [Server]
    public void AbortTransition(string reason = "Manual abort")
    {
        if (currentTransitionState != TransitionState.Idle && currentTransitionState != TransitionState.Complete)
        {
            LogTransitionError($"Transition aborted: {reason}");
            
            if (transitionCoroutine != null)
            {
                StopCoroutine(transitionCoroutine);
                transitionCoroutine = null;
            }

            SetTransitionState(TransitionState.Failed);
            
            // Reset to idle after a delay
            StartCoroutine(ResetToIdleAfterDelay(2f));
        }
    }

    private IEnumerator ResetToIdleAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);

        ResetToIdleSync();
    }

    private void ResetToIdleSync()
    {
        SetTransitionState(TransitionState.Idle);
        UpdateTransitionProgress(0f);

        // Clear transition data
        playerTransitionStates.Clear();
        playersReadyForTransition.Clear();
        playersSpawned.Clear();
    }

    private void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }
}
