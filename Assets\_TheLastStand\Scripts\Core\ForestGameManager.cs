using UnityEngine;
using Mirror;
using System.Collections;

public class ForestGameManager : GameManager
{
    [Header("Core References")]
    [SerializeField] private ForestIntroHelicopter forestIntroHelicopter;
    [SerializeField] private ForestMap_AudioManager forestMapAudioManager;
    [SerializeField] private ForestPlayerManager forestPlayerManager;
    [SerializeField] private HelicopterImmobilizationManager immobilizationManager;

    [Header("Initialization Settings")]
    [SerializeField] private float systemInitializationDelay = 1f;
    [SerializeField] private float helicopterStartDelay = 2f;
    [SerializeField] private float audioStartDelay = 0.5f;
    [SerializeField] private bool enableDetailedLogging = true;
    [SerializeField] private bool useHelicopterAutoStart = true;

    // State tracking
    private bool isInitialized = false;
    private bool hasStartedIntroSequence = false;

    public void Start()
    {
        StartCoroutine(InitializeForestScene());
    }

    private IEnumerator InitializeForestScene()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Starting forest scene initialization");
        }

        // Wait for initial system stabilization
        yield return new WaitForSeconds(systemInitializationDelay);

        bool initializationSuccessful = false;

        // Phase 1: Validate and find required components
        yield return StartCoroutine(ValidateAndFindComponents());

        // Phase 2: Validate scene state
        if (!ValidateSceneState())
        {
            Debug.LogError("ForestGameManager: Scene state validation failed");
            HandleInitializationFailure();
            yield break;
        }

        // Phase 3: Initialize audio systems
        yield return StartCoroutine(InitializeAudioSystems());

        // Phase 4: Wait for players to be ready
        yield return StartCoroutine(WaitForPlayersReady());

        // Phase 5: Start intro sequence (if not using auto-start)
        if (!useHelicopterAutoStart)
        {
            yield return StartCoroutine(StartIntroSequence());
        }
        else if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Skipping manual helicopter start - using auto-start mode");
        }

        // Check if all phases completed successfully
        initializationSuccessful = true;
        isInitialized = initializationSuccessful;

        if (initializationSuccessful)
        {
            if (enableDetailedLogging)
            {
                Debug.Log("ForestGameManager: Forest scene initialization completed successfully");
                LogSceneStatus();
            }
        }
        else
        {
            Debug.LogError("ForestGameManager: Forest scene initialization failed");
            HandleInitializationFailure();
        }
    }

    private IEnumerator ValidateAndFindComponents()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Validating and finding components");
        }

        bool validationFailed = false;

        // Find ForestPlayerManager
        if (forestPlayerManager == null)
        {
            forestPlayerManager = FindObjectOfType<ForestPlayerManager>();
            if (forestPlayerManager == null)
            {
                Debug.LogError("ForestGameManager: ForestPlayerManager not found in scene");
                validationFailed = true;
            }
            else if (enableDetailedLogging)
            {
                Debug.Log("ForestGameManager: Found ForestPlayerManager");
            }
        }

        // Validate ForestPlayerManager state
        if (forestPlayerManager != null && !forestPlayerManager.ValidateManagerState())
        {
            Debug.LogError("ForestGameManager: ForestPlayerManager validation failed");
            validationFailed = true;
        }

        // Find HelicopterImmobilizationManager
        if (immobilizationManager == null)
        {
            immobilizationManager = FindObjectOfType<HelicopterImmobilizationManager>();
            if (immobilizationManager == null)
            {
                Debug.LogWarning("ForestGameManager: HelicopterImmobilizationManager not found. Player immobilization during helicopter sequence may not work.");
            }
            else if (enableDetailedLogging)
            {
                Debug.Log("ForestGameManager: Found HelicopterImmobilizationManager");
            }
        }

        // Validate helicopter reference
        if (forestIntroHelicopter == null)
        {
            Debug.LogError("ForestGameManager: ForestIntroHelicopter not assigned in the Inspector.");
            validationFailed = true;
        }

        // Validate audio manager reference
        if (forestMapAudioManager == null)
        {
            Debug.LogError("ForestGameManager: ForestMapAudioManager not assigned in the Inspector.");
            validationFailed = true;
        }

        if (validationFailed)
        {
            HandleInitializationFailure();
        }

        yield return null;
    }

    private bool ValidateSceneState()
    {
        // Check networking state
        if (MyNetworkManager.isMultiplayer && !NetworkServer.active)
        {
            Debug.LogError("ForestGameManager: Multiplayer mode but server is not active");
            return false;
        }

        // Check scene name
        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        if (currentScene != "Map_01")
        {
            Debug.LogError($"ForestGameManager: Expected scene 'Map_01' but current scene is '{currentScene}'");
            return false;
        }

        return true;
    }

    private IEnumerator InitializeAudioSystems()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Initializing audio systems");
        }

        yield return new WaitForSeconds(audioStartDelay);

        bool audioInitialized = InitializeAudioSystemsSync();

        if (audioInitialized && enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Audio systems initialized successfully");
        }
    }

    private bool InitializeAudioSystemsSync()
    {
        try
        {
            if (forestMapAudioManager != null)
            {
                forestMapAudioManager.PlayIntroStormSound();
                forestMapAudioManager.PlayIntroBackgroundMusic();
                forestMapAudioManager.Invoke("PlayNarratorIntro", 2f);
                return true;
            }
            return false;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestGameManager: Error initializing audio systems: {ex.Message}");
            return false;
        }
    }

    private IEnumerator WaitForPlayersReady()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Waiting for players to be ready");
        }

        float waitStartTime = Time.time;
        float maxWaitTime = 10f; // Maximum wait time for players

        // Wait for players to spawn and be configured
        while (Time.time - waitStartTime < maxWaitTime)
        {
            if (forestPlayerManager.HasSpawnedPlayers())
            {
                // Additional wait to ensure all players are fully configured
                yield return new WaitForSeconds(1f);
                break;
            }

            yield return new WaitForSeconds(0.1f);
        }

        if (!forestPlayerManager.HasSpawnedPlayers())
        {
            Debug.LogWarning("ForestGameManager: Timeout waiting for players to spawn. Continuing with intro sequence.");
        }
        else if (enableDetailedLogging)
        {
            Debug.Log($"ForestGameManager: Players ready. Active player count: {forestPlayerManager.GetActivePlayerCount()}");
        }
    }

    private IEnumerator StartIntroSequence()
    {
        if (hasStartedIntroSequence)
        {
            Debug.LogWarning("ForestGameManager: Intro sequence already started");
            yield break;
        }

        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Starting helicopter intro sequence");
        }

        yield return new WaitForSeconds(helicopterStartDelay);

        bool introStarted = StartIntroSequenceSync();

        if (introStarted)
        {
            hasStartedIntroSequence = true;
            if (enableDetailedLogging)
            {
                Debug.Log("ForestGameManager: Helicopter intro sequence started successfully");
            }
        }
    }

    private bool StartIntroSequenceSync()
    {
        try
        {
            if (forestIntroHelicopter != null)
            {
                if (useHelicopterAutoStart)
                {
                    // Use manual start as fallback for auto-start
                    forestIntroHelicopter.ManualStartIntroSequence();
                    if (enableDetailedLogging)
                    {
                        Debug.Log("ForestGameManager: Triggered manual start as fallback for auto-start");
                    }
                }
                else
                {
                    // Traditional manual start
                    forestIntroHelicopter.StartIntroSequence();
                    if (enableDetailedLogging)
                    {
                        Debug.Log("ForestGameManager: Started helicopter intro sequence manually");
                    }
                }
                return true;
            }
            else
            {
                Debug.LogError("ForestGameManager: Cannot start intro sequence - ForestIntroHelicopter is null");
                return false;
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestGameManager: Error starting intro sequence: {ex.Message}");
            return false;
        }
    }

    public void OnHelicopterLanded()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestGameManager: Helicopter landed, handling post-landing actions");
        }

        HandleHelicopterLandedSync();
    }

    private void HandleHelicopterLandedSync()
    {
        try
        {
            // Fade out the helicopter sound
            if (AudioSettings.Instance != null && forestIntroHelicopter != null)
            {
                AudioSource helicopterAudio = forestIntroHelicopter.GetComponent<AudioSource>();
                if (helicopterAudio != null)
                {
                    AudioSettings.Instance.FadeOutSound(helicopterAudio, 2f);
                }
            }

            // Additional post-landing logic can be added here
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestGameManager: Error handling helicopter landing: {ex.Message}");
        }
    }

    private void HandleInitializationFailure()
    {
        Debug.LogError("ForestGameManager: Initialization failed. Attempting recovery...");

        // Attempt basic recovery
        try
        {
            // Try to at least start audio if possible
            if (forestMapAudioManager != null)
            {
                forestMapAudioManager.PlayIntroBackgroundMusic();
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"ForestGameManager: Recovery attempt failed: {ex.Message}");
        }
    }

    private void LogSceneStatus()
    {
        Debug.Log($"ForestGameManager: Scene Status - " +
                 $"Multiplayer: {MyNetworkManager.isMultiplayer}, " +
                 $"NetworkServer.active: {NetworkServer.active}, " +
                 $"Players: {forestPlayerManager.GetActivePlayerCount()}, " +
                 $"Intro Started: {hasStartedIntroSequence}");

        if (enableDetailedLogging && forestPlayerManager != null)
        {
            Debug.Log(forestPlayerManager.GetStatusInfo());
        }
    }

    /// <summary>
    /// Public method to check if the scene is properly initialized
    /// </summary>
    public bool IsSceneInitialized()
    {
        return isInitialized;
    }

    /// <summary>
    /// Public method to check if intro sequence has started
    /// </summary>
    public bool HasIntroSequenceStarted()
    {
        return hasStartedIntroSequence;
    }

    /// <summary>
    /// Force restart the intro sequence (for debugging/recovery)
    /// </summary>
    public void ForceRestartIntroSequence()
    {
        if (forestIntroHelicopter != null)
        {
            Debug.LogWarning("ForestGameManager: Force restarting intro sequence");
            hasStartedIntroSequence = false;
            StartCoroutine(StartIntroSequence());
        }
    }
}
