using UnityEngine;
using Mirror;
using System.Collections;

/// <summary>
/// Integration test for the enhanced scene transition system.
/// This script can be used to test the transition system in both editor and runtime.
/// </summary>
public class SceneTransitionIntegrationTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private bool enableDetailedLogging = true;
    [SerializeField] private float testDelay = 2f;

    [Header("Test Results")]
    [SerializeField] private bool lastTestPassed = false;
    [SerializeField] private string lastTestResult = "";

    private void Start()
    {
        if (runTestOnStart)
        {
            StartCoroutine(RunIntegrationTestAfterDelay());
        }
    }

    private IEnumerator RunIntegrationTestAfterDelay()
    {
        yield return new WaitForSeconds(testDelay);
        RunIntegrationTest();
    }

    /// <summary>
    /// Runs a comprehensive integration test of the scene transition system
    /// </summary>
    [ContextMenu("Run Integration Test")]
    public void RunIntegrationTest()
    {
        StartCoroutine(ExecuteIntegrationTest());
    }

    private IEnumerator ExecuteIntegrationTest()
    {
        LogTest("Starting Scene Transition Integration Test");
        bool testPassed = true;
        System.Text.StringBuilder results = new System.Text.StringBuilder();

        // Test 1: Validate core components exist
        LogTest("Test 1: Validating core components");
        if (!ValidateCoreComponents())
        {
            testPassed = false;
            results.AppendLine("FAIL: Core components validation failed");
        }
        else
        {
            results.AppendLine("PASS: Core components validation");
        }

        yield return new WaitForSeconds(0.1f);

        // Test 2: Validate scene transition prerequisites
        LogTest("Test 2: Validating scene transition prerequisites");
        bool prerequisitesValid = ValidateTransitionPrerequisitesSync(results);
        if (!prerequisitesValid)
        {
            testPassed = false;
        }

        yield return new WaitForSeconds(0.1f);

        // Test 3: Test SceneTransitionManager state
        LogTest("Test 3: Testing SceneTransitionManager state");
        if (!ValidateSceneTransitionManagerSync(results))
        {
            testPassed = false;
        }

        yield return new WaitForSeconds(0.1f);

        // Test 4: Test LobbyController integration
        LogTest("Test 4: Testing LobbyController integration");
        if (!ValidateLobbyControllerSync(results))
        {
            testPassed = false;
        }

        yield return new WaitForSeconds(0.1f);

        // Test 5: Test networking state
        LogTest("Test 5: Testing networking state");
        if (!ValidateNetworkingState())
        {
            testPassed = false;
            results.AppendLine("FAIL: Networking state validation failed");
        }
        else
        {
            results.AppendLine("PASS: Networking state validation");
        }

        yield return new WaitForSeconds(0.1f);

        // Test 6: Test current scene specific components
        LogTest("Test 6: Testing scene-specific components");
        if (!ValidateSceneSpecificComponentsSync(results))
        {
            testPassed = false;
        }

        // Final results
        lastTestPassed = testPassed;
        lastTestResult = results.ToString();

        LogTest($"Integration Test {(testPassed ? "PASSED" : "FAILED")}");
        LogTest("Test Results:");
        LogTest(lastTestResult);
    }

    private bool ValidateTransitionPrerequisitesSync(System.Text.StringBuilder results)
    {
        try
        {
            var validationReports = SceneTransitionValidator.ValidateTransitionPrerequisites();
            bool hasBlockingIssues = SceneTransitionValidator.HasBlockingIssues(validationReports);

            if (hasBlockingIssues)
            {
                results.AppendLine("FAIL: Scene transition prerequisites validation failed");
                results.AppendLine($"  {SceneTransitionValidator.GetValidationSummary(validationReports)}");
                return false;
            }
            else
            {
                results.AppendLine("PASS: Scene transition prerequisites validation");
                results.AppendLine($"  {SceneTransitionValidator.GetValidationSummary(validationReports)}");
                return true;
            }
        }
        catch (System.Exception ex)
        {
            results.AppendLine($"EXCEPTION: Prerequisites validation failed: {ex.Message}");
            return false;
        }
    }

    private bool ValidateSceneTransitionManagerSync(System.Text.StringBuilder results)
    {
        if (SceneTransitionManager.Instance == null)
        {
            results.AppendLine("FAIL: SceneTransitionManager.Instance is null");
            return false;
        }
        else
        {
            results.AppendLine("PASS: SceneTransitionManager.Instance exists");
            results.AppendLine($"  Current State: {SceneTransitionManager.Instance.CurrentState}");
            results.AppendLine($"  Progress: {SceneTransitionManager.Instance.TransitionProgress:P}");
            return true;
        }
    }

    private bool ValidateLobbyControllerSync(System.Text.StringBuilder results)
    {
        if (LobbyController.instance == null)
        {
            results.AppendLine("FAIL: LobbyController.instance is null");
            return false;
        }
        else
        {
            results.AppendLine("PASS: LobbyController.instance exists");
            results.AppendLine($"  Transition in progress: {LobbyController.instance.IsTransitionInProgress()}");
            results.AppendLine($"  Transition attempts: {LobbyController.instance.GetTransitionAttempts()}");
            return true;
        }
    }

    private bool ValidateSceneSpecificComponentsSync(System.Text.StringBuilder results)
    {
        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        if (currentScene == "Map_01")
        {
            if (!ValidateMap01Components())
            {
                results.AppendLine("FAIL: Map_01 components validation failed");
                return false;
            }
            else
            {
                results.AppendLine("PASS: Map_01 components validation");
                return true;
            }
        }
        else if (currentScene == "MainMenu")
        {
            if (!ValidateMainMenuComponents())
            {
                results.AppendLine("FAIL: MainMenu components validation failed");
                return false;
            }
            else
            {
                results.AppendLine("PASS: MainMenu components validation");
                return true;
            }
        }
        else
        {
            results.AppendLine($"INFO: Unknown scene '{currentScene}', skipping scene-specific validation");
            return true;
        }
    }

    private bool ValidateCoreComponents()
    {
        bool isValid = true;

        // Check NetworkManager
        if (NetworkManager.singleton == null)
        {
            LogTest("ERROR: NetworkManager.singleton is null");
            isValid = false;
        }

        // Check MyNetworkManager
        if (MyNetworkManager.instance == null)
        {
            LogTest("ERROR: MyNetworkManager.instance is null");
            isValid = false;
        }

        // Check SceneTransitionManager
        if (SceneTransitionManager.Instance == null)
        {
            LogTest("WARNING: SceneTransitionManager.Instance is null (may be expected in some scenes)");
        }

        // Check LobbyController
        if (LobbyController.instance == null)
        {
            LogTest("ERROR: LobbyController.instance is null");
            isValid = false;
        }

        return isValid;
    }

    private bool ValidateNetworkingState()
    {
        bool isValid = true;

        // Check if multiplayer mode is properly set
        LogTest($"Multiplayer mode: {MyNetworkManager.isMultiplayer}");
        LogTest($"NetworkServer.active: {NetworkServer.active}");
        LogTest($"NetworkClient.active: {NetworkClient.active}");

        // Check player prefab
        if (NetworkManager.singleton != null && NetworkManager.singleton.playerPrefab == null)
        {
            LogTest("ERROR: Player prefab is not assigned");
            isValid = false;
        }

        // Check LobbyPlayerList
        if (LobbyPlayerList.instance == null)
        {
            LogTest("ERROR: LobbyPlayerList.instance is null");
            isValid = false;
        }
        else
        {
            LogTest($"Connected players: {LobbyPlayerList.instance.allClients.Count}");
        }

        return isValid;
    }

    private bool ValidateMainMenuComponents()
    {
        bool isValid = true;

        // Check MainMenu
        if (MainMenu.instance == null)
        {
            LogTest("ERROR: MainMenu.instance is null");
            isValid = false;
        }

        // Check SteamLobby
        SteamLobby steamLobby = FindObjectOfType<SteamLobby>();
        if (steamLobby == null)
        {
            LogTest("ERROR: SteamLobby not found");
            isValid = false;
        }

        return isValid;
    }

    private bool ValidateMap01Components()
    {
        bool isValid = true;

        // Check ForestGameManager
        ForestGameManager forestGameManager = FindObjectOfType<ForestGameManager>();
        if (forestGameManager == null)
        {
            LogTest("ERROR: ForestGameManager not found");
            isValid = false;
        }
        else
        {
            LogTest($"ForestGameManager initialized: {forestGameManager.IsSceneInitialized()}");
            LogTest($"Intro sequence started: {forestGameManager.HasIntroSequenceStarted()}");
        }

        // Check ForestPlayerManager
        ForestPlayerManager forestPlayerManager = FindObjectOfType<ForestPlayerManager>();
        if (forestPlayerManager == null)
        {
            LogTest("ERROR: ForestPlayerManager not found");
            isValid = false;
        }
        else
        {
            LogTest($"Players spawned: {forestPlayerManager.HasSpawnedPlayers()}");
            LogTest($"Active player count: {forestPlayerManager.GetActivePlayerCount()}");
            
            if (!forestPlayerManager.ValidateManagerState())
            {
                LogTest("ERROR: ForestPlayerManager validation failed");
                isValid = false;
            }
        }

        // Check ForestIntroHelicopter
        ForestIntroHelicopter helicopter = FindObjectOfType<ForestIntroHelicopter>();
        if (helicopter == null)
        {
            LogTest("ERROR: ForestIntroHelicopter not found");
            isValid = false;
        }
        else
        {
            LogTest($"Helicopter state: {helicopter.CurrentState}");
        }

        // Check HelicopterImmobilizationManager
        HelicopterImmobilizationManager immobilizationManager = FindObjectOfType<HelicopterImmobilizationManager>();
        if (immobilizationManager == null)
        {
            LogTest("WARNING: HelicopterImmobilizationManager not found");
        }
        else
        {
            LogTest($"Immobilization active: {immobilizationManager.GlobalImmobilizationActive}");
        }

        return isValid;
    }

    private void LogTest(string message)
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"[SceneTransitionTest] {message}");
        }
    }

    /// <summary>
    /// Get the last test results for external inspection
    /// </summary>
    public string GetLastTestResults()
    {
        return lastTestResult;
    }

    /// <summary>
    /// Check if the last test passed
    /// </summary>
    public bool DidLastTestPass()
    {
        return lastTestPassed;
    }

    /// <summary>
    /// Manual test trigger for specific validation
    /// </summary>
    [ContextMenu("Test Scene Transition Prerequisites")]
    public void TestSceneTransitionPrerequisites()
    {
        var reports = SceneTransitionValidator.ValidateTransitionPrerequisites();
        SceneTransitionValidator.LogValidationReports(reports, "Manual Test");
        
        LogTest($"Manual validation result: {SceneTransitionValidator.GetValidationSummary(reports)}");
        LogTest($"Has blocking issues: {SceneTransitionValidator.HasBlockingIssues(reports)}");
    }

    /// <summary>
    /// Manual test trigger for Map_01 scene validation
    /// </summary>
    [ContextMenu("Test Map_01 Scene State")]
    public void TestMap01SceneState()
    {
        var reports = SceneTransitionValidator.ValidateMap01SceneState();
        SceneTransitionValidator.LogValidationReports(reports, "Map_01 Test");
        
        LogTest($"Map_01 validation result: {SceneTransitionValidator.GetValidationSummary(reports)}");
        LogTest($"Has blocking issues: {SceneTransitionValidator.HasBlockingIssues(reports)}");
    }
}
