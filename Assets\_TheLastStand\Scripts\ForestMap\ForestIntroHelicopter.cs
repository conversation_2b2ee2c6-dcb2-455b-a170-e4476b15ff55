using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class ForestIntroHelicopter : Helicopter
{
    [Header("Forest Intro Specific Settings")]
    [SerializeField] private float specificLandingTime = 3f;
    [SerializeField] private float autoStartDelay = 0.5f;
    [SerializeField] private bool enableAutoStart = true;
    [SerializeField] private bool enableEnhancedRotation = true;
    [SerializeField] private bool enableDetailedLogging = false;

    [Header("Enhanced Rotation Settings")]
    [SerializeField] private Vector3 helicopterOrientationOffset = new Vector3(-90f, 0f, 0f);
    [SerializeField] private float rotationCompletionThreshold = 5.0f;
    [SerializeField] private float maxRotationTime = 3.0f;
    [SerializeField] private bool enableYRotationDebugging = false;

    private Transform currentTargetWaypointTransform;
    private bool hasAutoStarted = false;

    public override void Start()
    {
        base.Start();

        // Validate helicopter orientation settings
        ValidateHelicopterOrientation();

        // Auto-assign waypoints if not already assigned
        if (waypoints == null || waypoints.Count == 0)
        {
            AutoAssignWaypoints();
        }

        if (enableAutoStart && !hasAutoStarted)
        {
            StartCoroutine(AutoStartIntroSequence());
        }
    }

    private void ValidateHelicopterOrientation()
    {
        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Helicopter orientation offset: {helicopterOrientationOffset}", this);
            Debug.Log($"ForestIntroHelicopter: Initial transform rotation: {transform.rotation.eulerAngles}", this);
            Debug.Log($"ForestIntroHelicopter: Enhanced rotation enabled: {enableEnhancedRotation}", this);
        }

        // Warn if the helicopter model reference is not properly set
        if (helicopterModel == null)
        {
            Debug.LogWarning("ForestIntroHelicopter: helicopterModel is not assigned. Enhanced rotation may not work as expected.", this);
        }
        else if (helicopterModel.transform == this.transform)
        {
            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Helicopter model is the same as this transform - using enhanced rotation system.", this);
            }
        }
    }

    private void AutoAssignWaypoints()
    {
        // Find the HelicopterWaypoints parent object
        GameObject waypointsParent = GameObject.Find("HelicopterWaypoints");
        if (waypointsParent != null)
        {
            if (waypoints == null)
            {
                waypoints = new List<Transform>();
            }
            else
            {
                waypoints.Clear();
            }

            // Add waypoints in order
            for (int i = 0; i < 4; i++)
            {
                Transform waypoint = waypointsParent.transform.Find($"HelicopterWaypoint_{i}");
                if (waypoint != null)
                {
                    waypoints.Add(waypoint);
                    if (enableDetailedLogging)
                    {
                        Debug.Log($"ForestIntroHelicopter: Auto-assigned waypoint {i}: {waypoint.name} at position {waypoint.position}", this);
                    }
                }
                else
                {
                    Debug.LogWarning($"ForestIntroHelicopter: Could not find HelicopterWaypoint_{i}", this);
                }
            }

            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Auto-assigned {waypoints.Count} waypoints successfully", this);
            }
        }
        else
        {
            Debug.LogError("ForestIntroHelicopter: Could not find HelicopterWaypoints parent object", this);
        }
    }

    private IEnumerator AutoStartIntroSequence()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Starting auto-start sequence", this);
        }

        // Wait for scene stabilization
        yield return new WaitForSeconds(autoStartDelay);

        // Validate waypoints before starting
        if (ValidateWaypoints())
        {
            hasAutoStarted = true;
            StartIntroSequence();

            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Auto-start completed successfully", this);
            }
        }
        else
        {
            Debug.LogError("ForestIntroHelicopter: Auto-start failed - waypoints validation failed", this);
        }
    }

    private bool ValidateWaypoints()
    {
        if (waypoints == null || waypoints.Count == 0)
        {
            Debug.LogError("ForestIntroHelicopter: No waypoints assigned", this);
            return false;
        }

        for (int i = 0; i < waypoints.Count; i++)
        {
            if (waypoints[i] == null)
            {
                Debug.LogError($"ForestIntroHelicopter: Waypoint at index {i} is null", this);
                return false;
            }
        }

        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Validated {waypoints.Count} waypoints successfully", this);
        }

        return true;
    }

    public void StartIntroSequence()
    {
        if (this.waypoints == null || this.waypoints.Count == 0)
        {
            Debug.LogError("ForestIntroHelicopter: Cannot start intro sequence - no waypoints", this);
            enabled = false;
            return;
        }

        if (waypoints.Count > 0) {
            currentTargetWaypointTransform = waypoints[0];
        }

        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Starting intro sequence with waypoint navigation", this);
        }

        BeginNavigation(this.waypoints, specificLandingTime);
    }

    public void OnNavigationCompleted()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Navigation completed", this);
        }
    }

    protected void Update()
    {
        // Apply enhanced rotation during both Rotating and MovingToWaypoint states
        if (enableEnhancedRotation &&
            (currentState == HelicopterState.Rotating || currentState == HelicopterState.MovingToWaypoint) &&
            activeTargetWaypoint != null)
        {
            PerformEnhancedRotationDuringFlight();
        }
    }

    private void PerformEnhancedRotationDuringFlight()
    {
        Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
        float distanceToWaypoint = directionToWaypoint.magnitude;

        // Enhanced debugging for the 292.4371° issue
        if (enableYRotationDebugging && Time.frameCount % 30 == 0)
        {
            Debug.Log($"=== Y-ROTATION ANALYSIS ===", this);
            Debug.Log($"Helicopter Position: {transform.position}", this);
            Debug.Log($"Waypoint Position: {activeTargetWaypoint.position}", this);
            Debug.Log($"Direction Vector: {directionToWaypoint}", this);
            Debug.Log($"Distance to Waypoint: {distanceToWaypoint:F4}", this);
        }

        // CRITICAL: Check if waypoint is too close (causing undefined rotation)
        if (distanceToWaypoint < 0.5f) // Increased threshold from 0.1f to 0.5f for better stability
        {
            if (enableDetailedLogging)
            {
                Debug.LogWarning($"ForestIntroHelicopter: Waypoint {activeTargetWaypoint.name} is too close ({distanceToWaypoint:F4}m) - attempting to advance to next waypoint", this);
            }

            // Try to advance to the next waypoint if current one is too close
            if (currentWaypointIndex + 1 < currentPathWaypoints.Count)
            {
                currentWaypointIndex++;
                activeTargetWaypoint = currentPathWaypoints[currentWaypointIndex];
                if (enableDetailedLogging)
                {
                    Debug.Log($"ForestIntroHelicopter: Advanced to next waypoint: {activeTargetWaypoint.name}", this);
                }
                // Recursively call this method with the new waypoint
                PerformEnhancedRotationDuringFlight();
                return;
            }
            else
            {
                if (enableDetailedLogging)
                {
                    Debug.LogWarning($"ForestIntroHelicopter: No more waypoints available - skipping rotation", this);
                }
                return; // Skip rotation when waypoint is too close and no more waypoints
            }
        }

        // Use horizontal direction only for Y-axis rotation calculation
        Vector3 horizontalDirection = new Vector3(directionToWaypoint.x, 0f, directionToWaypoint.z);

        if (horizontalDirection.sqrMagnitude > 0.25f) // Increased threshold for more stable rotation
        {
            horizontalDirection.Normalize();

            // IMPROVED: Use a more robust rotation calculation approach
            // Instead of raw Atan2, use Unity's built-in LookRotation for better stability
            Quaternion lookRotation = Quaternion.LookRotation(horizontalDirection, Vector3.up);
            float targetYRotation = lookRotation.eulerAngles.y;

            // Construct final rotation using individual axis values
            Vector3 targetEulerAngles = new Vector3(
                helicopterOrientationOffset.x,  // Fixed X-axis rotation (typically -90°)
                targetYRotation,                // Y-axis rotation to face waypoint [0-360°]
                helicopterOrientationOffset.z   // Fixed Z-axis rotation (typically 0°)
            );

            Quaternion targetRotation = Quaternion.Euler(targetEulerAngles);

            // Apply smooth rotation interpolation
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * rotationSpeed);

            if (enableDetailedLogging && Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
            {
                Debug.Log($"ForestIntroHelicopter: Rotating toward waypoint {activeTargetWaypoint.name}", this);
                Debug.Log($"  Horizontal Direction: {horizontalDirection}", this);
                Debug.Log($"  Target Y-Rotation: {targetYRotation:F2}°", this);
                Debug.Log($"  Target Euler: {targetEulerAngles}", this);
                Debug.Log($"  Current Euler: {transform.rotation.eulerAngles}", this);
            }

            // Enhanced Y-rotation debugging for the 292.4371° issue
            if (enableYRotationDebugging && Time.frameCount % 30 == 0)
            {
                Vector3 currentEuler = transform.rotation.eulerAngles;
                Debug.Log($"Y-ROTATION DEBUG: Current Y={currentEuler.y:F4}°, Target Y={targetYRotation:F4}°", this);
                Debug.Log($"Angle Difference: {Mathf.DeltaAngle(currentEuler.y, targetYRotation):F4}°", this);

                // Also show the old Atan2 calculation for comparison
                float oldAtan2Result = Mathf.Atan2(horizontalDirection.x, horizontalDirection.z) * Mathf.Rad2Deg;
                while (oldAtan2Result < 0f) oldAtan2Result += 360f;
                while (oldAtan2Result >= 360f) oldAtan2Result -= 360f;
                Debug.Log($"Old Atan2 Result: {oldAtan2Result:F4}° vs New LookRotation: {targetYRotation:F4}°", this);

                // Validate that the rotation makes sense for forward movement
                if (Mathf.Abs(Mathf.DeltaAngle(targetYRotation, 0f)) > 90f)
                {
                    Debug.LogWarning($"⚠ Target Y-Rotation {targetYRotation:F2}° is not forward-facing (expected ~0°)", this);
                    Debug.LogWarning($"  This suggests the helicopter is moving backward or sideways to the waypoint", this);
                    Debug.LogWarning($"  Direction: {horizontalDirection}, Distance: {distanceToWaypoint:F4}m", this);
                }
                else
                {
                    Debug.Log($"✓ Target Y-Rotation {targetYRotation:F2}° is forward-facing (within ±90° of 0°)", this);
                }
            }
        }
        else
        {
            if (enableDetailedLogging)
            {
                Debug.LogWarning($"ForestIntroHelicopter: Horizontal direction too small for waypoint {activeTargetWaypoint.name} - maintaining current rotation", this);
            }
        }
    }

    /// <summary>
    /// Override the base class rotation behavior to use our enhanced rotation system
    /// </summary>
    protected override IEnumerator Execute_RotatingState()
    {
        if (activeTargetWaypoint == null)
        {
            Debug.LogError("ForestIntroHelicopter: Rotation state - Target waypoint is null.", this);
            TransitionToState(HelicopterState.None);
            yield break;
        }

        if (enableEnhancedRotation)
        {
            // Use our enhanced rotation system instead of the base class rotation
            // The Update method will handle the rotation via PerformEnhancedRotationDuringFlight()

            // Wait for rotation to complete (check angle threshold)
            float rotationStartTime = Time.time;

            while (Time.time - rotationStartTime < maxRotationTime)
            {
                Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
                float distanceToWaypoint = directionToWaypoint.magnitude;

                // Skip rotation if waypoint is too close
                if (distanceToWaypoint < 0.1f)
                {
                    if (enableDetailedLogging)
                    {
                        Debug.LogWarning($"ForestIntroHelicopter: Waypoint too close in rotation state - completing rotation", this);
                    }
                    break;
                }

                // Use horizontal direction only for Y-axis rotation
                Vector3 horizontalDirection = new Vector3(directionToWaypoint.x, 0f, directionToWaypoint.z);

                if (horizontalDirection.sqrMagnitude > 0.01f)
                {
                    horizontalDirection.Normalize();

                    // Use same Y-axis calculation logic as PerformEnhancedRotationDuringFlight
                    float targetYRotation = Mathf.Atan2(horizontalDirection.x, horizontalDirection.z) * Mathf.Rad2Deg;

                    // Keep Y-rotation in Unity's [0, 360] range
                    while (targetYRotation < 0f) targetYRotation += 360f;
                    while (targetYRotation >= 360f) targetYRotation -= 360f;

                    Vector3 targetEulerAngles = new Vector3(
                        helicopterOrientationOffset.x,
                        targetYRotation,
                        helicopterOrientationOffset.z
                    );

                    Quaternion targetRotation = Quaternion.Euler(targetEulerAngles);

                    float angleDifference = Quaternion.Angle(transform.rotation, targetRotation);
                    if (angleDifference <= rotationCompletionThreshold)
                    {
                        break; // Rotation complete
                    }
                }
                yield return null;
            }

            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Enhanced rotation completed for waypoint {activeTargetWaypoint.name}", this);
            }
        }
        else
        {
            // Fall back to base class rotation behavior
            yield return StartCoroutine(base.Execute_RotatingState());
            yield break;
        }

        // Transition to next state
        if (IsApproachingFinalSegment())
        {
            TransitionToState(HelicopterState.Landing);
        }
        else
        {
            TransitionToState(HelicopterState.MovingToWaypoint);
        }
    }

    /// <summary>
    /// Helper method to check if approaching final segment (copied from base class)
    /// </summary>
    private bool IsApproachingFinalSegment()
    {
        return currentWaypointIndex + 1 >= currentPathWaypoints.Count - 1;
    }

    protected void OnWaypointReached(Transform reachedWaypoint)
    {
        int reachedWaypointIndex = -1;
        if (this.waypoints != null)
        {
            reachedWaypointIndex = this.waypoints.IndexOf(reachedWaypoint);
        }

        if (reachedWaypointIndex != -1)
        {
            if (enableDetailedLogging)
            {
                Debug.Log($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}' (Index: {reachedWaypointIndex}). Base class will handle progression.", this);
            }
        }
        else
        {
            Debug.LogWarning($"ForestIntroHelicopter: Reached waypoint '{reachedWaypoint.name}', but it was not found in the assigned waypoints list. This could indicate a configuration issue.", this);
        }
    }

    /// <summary>
    /// Manual start method for backward compatibility with ForestGameManager
    /// </summary>
    public void ManualStartIntroSequence()
    {
        if (!hasAutoStarted)
        {
            if (enableDetailedLogging)
            {
                Debug.Log("ForestIntroHelicopter: Manual start requested", this);
            }

            hasAutoStarted = true;
            StartIntroSequence();
        }
        else if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Manual start requested but auto-start already completed", this);
        }
    }

    /// <summary>
    /// Force restart the intro sequence (for debugging or special cases)
    /// </summary>
    public void ForceRestartIntroSequence()
    {
        if (enableDetailedLogging)
        {
            Debug.Log("ForestIntroHelicopter: Force restart requested", this);
        }

        hasAutoStarted = true;
        StartIntroSequence();
    }

    /// <summary>
    /// Public method to manually assign waypoints (for testing and debugging)
    /// </summary>
    [ContextMenu("Assign Waypoints")]
    public void ManuallyAssignWaypoints()
    {
        AutoAssignWaypoints();
        if (enableDetailedLogging)
        {
            Debug.Log($"ForestIntroHelicopter: Manually assigned waypoints. Count: {(waypoints != null ? waypoints.Count : 0)}", this);
        }
    }

    /// <summary>
    /// Test method to validate helicopter orientation (for debugging)
    /// </summary>
    [ContextMenu("Test Helicopter Orientation")]
    public void TestHelicopterOrientation()
    {
        Debug.Log("=== ForestIntroHelicopter Orientation Test ===", this);
        Debug.Log($"Current Transform Rotation: {transform.rotation.eulerAngles}", this);
        Debug.Log($"Helicopter Orientation Offset: {helicopterOrientationOffset}", this);
        Debug.Log($"Enhanced Rotation Enabled: {enableEnhancedRotation}", this);
        Debug.Log($"Rotation Speed: {rotationSpeed}", this);
        Debug.Log($"Rotation Completion Threshold: {rotationCompletionThreshold}°", this);
        Debug.Log($"Max Rotation Time: {maxRotationTime}s", this);

        if (activeTargetWaypoint != null)
        {
            Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
            if (directionToWaypoint.sqrMagnitude > 0.01f)
            {
                directionToWaypoint.Normalize();

                // Use same Y-axis calculation logic as the enhanced rotation system
                float targetYRotation = Mathf.Atan2(directionToWaypoint.x, directionToWaypoint.z) * Mathf.Rad2Deg;

                // Normalize Y-axis rotation to [-180, 180] range
                while (targetYRotation > 180f) targetYRotation -= 360f;
                while (targetYRotation < -180f) targetYRotation += 360f;

                Vector3 targetEulerAngles = new Vector3(
                    helicopterOrientationOffset.x,
                    targetYRotation,
                    helicopterOrientationOffset.z
                );

                Quaternion targetRotation = Quaternion.Euler(targetEulerAngles);

                Debug.Log($"Direction to Waypoint: {directionToWaypoint}", this);
                Debug.Log($"Calculated Y-Rotation: {targetYRotation:F2}°", this);
                Debug.Log($"Target Euler Angles: {targetEulerAngles}", this);
                Debug.Log($"Target Rotation: {targetRotation.eulerAngles}", this);
                Debug.Log($"Angle Difference: {Quaternion.Angle(transform.rotation, targetRotation):F2}°", this);
            }
        }
        else
        {
            Debug.Log("No active target waypoint for orientation test", this);
        }
        Debug.Log("=== End Orientation Test ===", this);
    }

    /// <summary>
    /// Debug method to specifically test Y-rotation calculation (for debugging the 292.4371° issue)
    /// </summary>
    [ContextMenu("Debug Y-Rotation Calculation")]
    public void DebugYRotationCalculation()
    {
        Debug.Log("=== Y-Rotation Debug Test ===", this);
        Debug.Log($"Current Transform Rotation: {transform.rotation.eulerAngles}", this);

        if (activeTargetWaypoint != null)
        {
            Vector3 directionToWaypoint = activeTargetWaypoint.position - transform.position;
            Debug.Log($"Helicopter Position: {transform.position}", this);
            Debug.Log($"Waypoint Position: {activeTargetWaypoint.position}", this);
            Debug.Log($"Direction Vector: {directionToWaypoint}", this);

            if (directionToWaypoint.sqrMagnitude > 0.01f)
            {
                directionToWaypoint.Normalize();
                Debug.Log($"Normalized Direction: {directionToWaypoint}", this);

                // Show step-by-step Y-rotation calculation
                float rawYRotation = Mathf.Atan2(directionToWaypoint.x, directionToWaypoint.z) * Mathf.Rad2Deg;
                Debug.Log($"Raw Y-Rotation (Atan2): {rawYRotation:F4}°", this);

                float normalizedYRotation = rawYRotation;
                int normalizationSteps = 0;
                while (normalizedYRotation > 180f)
                {
                    normalizedYRotation -= 360f;
                    normalizationSteps++;
                }
                while (normalizedYRotation < -180f)
                {
                    normalizedYRotation += 360f;
                    normalizationSteps++;
                }

                Debug.Log($"Normalized Y-Rotation: {normalizedYRotation:F4}° (normalization steps: {normalizationSteps})", this);

                Vector3 finalEulerAngles = new Vector3(
                    helicopterOrientationOffset.x,
                    normalizedYRotation,
                    helicopterOrientationOffset.z
                );

                Debug.Log($"Final Euler Angles: {finalEulerAngles}", this);
                Debug.Log($"Expected Y-Rotation for forward facing: ~0°", this);

                // Test if the helicopter should be facing roughly forward
                if (Mathf.Abs(normalizedYRotation) < 10f)
                {
                    Debug.Log("✓ Y-Rotation is close to 0° - helicopter should face forward correctly", this);
                }
                else
                {
                    Debug.LogWarning($"⚠ Y-Rotation is {normalizedYRotation:F2}° - helicopter may not face forward correctly", this);
                }
            }
        }
        else
        {
            Debug.Log("No active target waypoint for Y-rotation test", this);
        }
        Debug.Log("=== End Y-Rotation Debug ===", this);
    }

    /// <summary>
    /// Debug method to analyze waypoint positioning and detect potential issues
    /// </summary>
    [ContextMenu("Analyze Waypoint Positioning")]
    public void AnalyzeWaypointPositioning()
    {
        Debug.Log("=== WAYPOINT POSITIONING ANALYSIS ===", this);
        Debug.Log($"Helicopter Position: {transform.position}", this);

        if (waypoints == null || waypoints.Count == 0)
        {
            Debug.LogError("No waypoints assigned!", this);
            return;
        }

        for (int i = 0; i < waypoints.Count; i++)
        {
            if (waypoints[i] != null)
            {
                Vector3 waypointPos = waypoints[i].position;
                Vector3 direction = waypointPos - transform.position;
                float distance = direction.magnitude;

                Debug.Log($"Waypoint {i} ({waypoints[i].name}):", this);
                Debug.Log($"  Position: {waypointPos}", this);
                Debug.Log($"  Distance from helicopter: {distance:F4}m", this);
                Debug.Log($"  Direction: {direction}", this);

                if (distance < 0.1f)
                {
                    Debug.LogError($"  ⚠ CRITICAL: Waypoint {i} is too close to helicopter ({distance:F4}m) - will cause rotation issues!", this);
                }
                else if (distance < 1.0f)
                {
                    Debug.LogWarning($"  ⚠ WARNING: Waypoint {i} is very close to helicopter ({distance:F4}m) - may cause unstable rotation", this);
                }
                else
                {
                    Vector3 horizontalDir = new Vector3(direction.x, 0f, direction.z).normalized;
                    float expectedYRotation = Mathf.Atan2(horizontalDir.x, horizontalDir.z) * Mathf.Rad2Deg;
                    while (expectedYRotation < 0f) expectedYRotation += 360f;
                    while (expectedYRotation >= 360f) expectedYRotation -= 360f;

                    Debug.Log($"  ✓ Expected Y-Rotation: {expectedYRotation:F2}°", this);
                }
            }
            else
            {
                Debug.LogError($"Waypoint {i} is null!", this);
            }
        }

        Debug.Log("=== END WAYPOINT ANALYSIS ===", this);
    }
}
